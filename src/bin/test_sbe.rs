use libwebsocket_rs::encoding::sbe::decoder::{SbeDecoder, SbeMessage};

fn main() {
    println!("Testing SBE Trade Parser...");

    // 使用之前成功解析的实际SBE数据进行测试
    let test_data = vec![
        18, 0, 16, 39, 1, 0, 0, 0, 188, 133, 193, 157, 233, 56, 6, 0, 99, 131, 193, 157, 233, 56,
        6, 0, 248, 248, 25, 0, 1, 0, 0, 0, 130, 150, 232, 41, 0, 0, 0, 0, 64, 161, 176, 138, 56, 0,
        0, 0, 224, 10, 0, 1, 0, 0, 0, 0, 0, 8, 69, 84, 72, 70, 68, 85, 83, 68,
    ];

    println!("Test data length: {} bytes", test_data.len());
    println!("Raw data: {:?}", test_data);

    if let Some(sbe_msg) = SbeDecoder::decode(&test_data) {
        match sbe_msg {
            SbeMessage::Trade(trade) => {
                println!("\n✅ Successfully parsed SBE trade message:");
                println!("   Symbol: {}", trade.symbol);
                println!("   Trade ID: {}", trade.trade_id);
                println!("   Price: {}", trade.price);
                println!("   Quantity: {}", trade.quantity);
                println!("   Trade Time: {}", trade.trade_time);
                println!("   Event Time: {}", trade.event_time);
                println!("   Is Buyer Maker: {}", trade.is_buyer_maker);
            }
            _ => {
                println!("❌ Decoded message is not a trade message");
            }
        }
    } else {
        println!("❌ Failed to decode SBE message");
    }

    // 测试批量数据
    println!("\n{}", "=".repeat(50));
    println!("Testing batch SBE data...");

    let batch_data = vec![
        18, 0, 16, 39, 1, 0, 0, 0, 107, 166, 204, 157, 233, 56, 6, 0, 235, 163, 204, 157, 233, 56,
        6, 0, 248, 248, 25, 0, 35, 0, 0, 0, 195, 0, 201, 0, 0, 0, 0, 0, 160, 228, 9, 3, 0, 0, 0, 0,
        128, 249, 181, 89, 3, 0, 0, 0, 0, 196, 0, 201, 0, 0, 0, 0, 0, 160, 228, 9, 3, 0, 0, 0, 0,
        0, 32, 112, 131, 5, 0, 0, 0, 0, 197, 0, 201, 0, 0, 0, 0, 0, 160, 228, 9, 3, 0, 0, 0, 0, 0,
        72, 53, 212, 8, 0, 0, 0, 0, 198, 0, 201, 0, 0, 0, 0, 0, 176, 11, 10, 3, 0, 0, 0, 0, 0, 49,
        204, 226, 1, 0, 0, 0, 0, 199, 0, 201, 0, 0, 0, 0, 0, 176, 11, 10, 3, 0, 0, 0, 0, 0, 12,
        119, 66, 3, 0, 0, 0, 0, 200, 0, 201, 0, 0, 0, 0, 0, 176, 11, 10, 3, 0, 0, 0, 0, 0, 126, 95,
        64, 0, 0, 0, 0, 0, 201, 0, 201, 0, 0, 0, 0, 0, 176, 11, 10, 3, 0, 0, 0, 0, 128, 208, 107,
        117, 0, 0, 0, 0, 0, 202, 0, 201, 0, 0, 0, 0, 0, 176, 11, 10, 3, 0, 0, 0, 0, 0, 2, 55, 88,
        0, 0, 0, 0, 0, 203, 0, 201, 0, 0, 0, 0, 0, 176, 11, 10, 3, 0, 0, 0, 0, 0, 214, 149, 85, 27,
        0, 0, 0, 0, 204, 0, 201, 0, 0, 0, 0, 0, 192, 50, 10, 3, 0, 0, 0, 0, 128, 208, 107, 117, 0,
        0, 0, 0, 0, 205, 0, 201, 0, 0, 0, 0, 0, 192, 50, 10, 3, 0, 0, 0, 0, 0, 12, 119, 66, 3, 0,
        0, 0, 0, 206, 0, 201, 0, 0, 0, 0, 0, 192, 50, 10, 3, 0, 0, 0, 0, 0, 12, 119, 66, 3, 0, 0,
        0, 0, 207, 0, 201, 0, 0, 0, 0, 0, 192, 50, 10, 3, 0, 0, 0, 0, 128, 31, 26, 10, 34, 1, 0, 0,
        0, 208, 0, 201, 0, 0, 0, 0, 0, 192, 50, 10, 3, 0, 0, 0, 0, 0, 2, 55, 88, 0, 0, 0, 0, 0,
        209, 0, 201, 0, 0, 0, 0, 0, 192, 50, 10, 3, 0, 0, 0, 0, 0, 126, 95, 64, 0, 0, 0, 0, 0, 210,
        0, 201, 0, 0, 0, 0, 0, 192, 50, 10, 3, 0, 0, 0, 0, 128, 170, 38, 252, 12, 0, 0, 0, 0, 211,
        0, 201, 0, 0, 0, 0, 0, 208, 89, 10, 3, 0, 0, 0, 0, 128, 208, 107, 117, 0, 0, 0, 0, 0, 212,
        0, 201, 0, 0, 0, 0, 0, 208, 89, 10, 3, 0, 0, 0, 0, 0, 2, 55, 88, 0, 0, 0, 0, 0, 213, 0,
        201, 0, 0, 0, 0, 0, 208, 89, 10, 3, 0, 0, 0, 0, 0, 12, 119, 66, 3, 0, 0, 0, 0, 214, 0, 201,
        0, 0, 0, 0, 0, 208, 89, 10, 3, 0, 0, 0, 0, 0, 12, 119, 66, 3, 0, 0, 0, 0, 215, 0, 201, 0,
        0, 0, 0, 0, 208, 89, 10, 3, 0, 0, 0, 0, 0, 2, 55, 88, 0, 0, 0, 0, 0, 216, 0, 201, 0, 0, 0,
        0, 0, 208, 89, 10, 3, 0, 0, 0, 0, 0, 126, 95, 64, 0, 0, 0, 0, 0, 217, 0, 201, 0, 0, 0, 0,
        0, 208, 89, 10, 3, 0, 0, 0, 0, 0, 247, 203, 60, 0, 0, 0, 0, 0, 218, 0, 201, 0, 0, 0, 0, 0,
        208, 89, 10, 3, 0, 0, 0, 0, 0, 185, 45, 249, 16, 0, 0, 0, 0, 219, 0, 201, 0, 0, 0, 0, 0,
        208, 89, 10, 3, 0, 0, 0, 0, 128, 246, 38, 159, 4, 0, 0, 0, 0, 220, 0, 201, 0, 0, 0, 0, 0,
        224, 128, 10, 3, 0, 0, 0, 0, 128, 208, 107, 117, 0, 0, 0, 0, 0, 221, 0, 201, 0, 0, 0, 0, 0,
        224, 128, 10, 3, 0, 0, 0, 0, 0, 12, 119, 66, 3, 0, 0, 0, 0, 222, 0, 201, 0, 0, 0, 0, 0,
        224, 128, 10, 3, 0, 0, 0, 0, 0, 12, 119, 66, 3, 0, 0, 0, 0, 223, 0, 201, 0, 0, 0, 0, 0,
        224, 128, 10, 3, 0, 0, 0, 0, 0, 126, 95, 64, 0, 0, 0, 0, 0, 224, 0, 201, 0, 0, 0, 0, 0,
        224, 128, 10, 3, 0, 0, 0, 0, 0, 184, 227, 189, 25, 0, 0, 0, 0, 225, 0, 201, 0, 0, 0, 0, 0,
        224, 128, 10, 3, 0, 0, 0, 0, 0, 111, 125, 64, 60, 0, 0, 0, 0, 226, 0, 201, 0, 0, 0, 0, 0,
        224, 128, 10, 3, 0, 0, 0, 0, 128, 195, 125, 198, 4, 0, 0, 0, 0, 227, 0, 201, 0, 0, 0, 0, 0,
        240, 167, 10, 3, 0, 0, 0, 0, 0, 58, 211, 116, 0, 0, 0, 0, 0, 228, 0, 201, 0, 0, 0, 0, 0,
        240, 167, 10, 3, 0, 0, 0, 0, 0, 12, 119, 66, 3, 0, 0, 0, 0, 229, 0, 201, 0, 0, 0, 0, 0,
        240, 167, 10, 3, 0, 0, 0, 0, 128, 99, 28, 89, 1, 0, 0, 0, 0, 9, 83, 89, 82, 85, 80, 85, 83,
        68, 84,
    ];

    println!("Batch data length: {} bytes", batch_data.len());

    if let Some(sbe_msg) = SbeDecoder::decode(&batch_data) {
        match sbe_msg {
            SbeMessage::Trade(trade) => {
                println!("\n✅ Successfully parsed batch SBE trade message:");
                println!("   Symbol: {}", trade.symbol);
                println!("   Trade ID: {}", trade.trade_id);
                println!("   Price: {}", trade.price);
                println!("   Quantity: {}", trade.quantity);
                println!("   Trade Time: {}", trade.trade_time);
                println!("   Event Time: {}", trade.event_time);
                println!("   Is Buyer Maker: {}", trade.is_buyer_maker);
            }
            _ => {
                println!("❌ Decoded batch message is not a trade message");
            }
        }
    } else {
        println!("❌ Failed to decode batch SBE message");
    }

    println!("\n🎉 SBE Trade Parser test completed!");
}
