use std::{
    net::{IpAddr, Ipv4Addr, SocketAddr},
    str::FromStr,
};

use mio::Token;

use crate::{
    CallbackData, EdgeDirection, Message, RING_FILLED_ORDERS, Result, Settings, TRADING_PAIR_RATES,
    TradingPair, WebSocket, WebSocketHandle,
    encoding::sbe::{::SbeMessage, parse_sbe_bookticker, parse_sbe_trade},
    engine::{
        self,
        arbitrage_engine::ArbitrageEngine,
        binance::{generate_market_data_ws_url, generate_order_url, generate_trade_ws_url},
        monitor::{CURRENT_RING_INDEX, CURRENT_RING_TS, init_latency_stats},
        trade::{
            generate_order_request_by_symbol, generate_order_requests,
            generate_session_logon_request, generate_trading_fee_request,
            generate_user_data_sub_request, update_trading_fee,
        },
        trading_pair::{API_KEY, TRADING_PAIR_COUNT},
    },
    net::utils::url::Url,
    utils::{self, perf::circles_to_ns},
};

const MARKET_DATA_TOKEN: Token = Token(0);
const ORDER_TOKEN_1: Token = Token(1);
const ORDER_TOKEN_2: Token = Token(2);
const ORDER_TOKEN_3: Token = Token(3);
const ORDER_TOKEN_4: Token = Token(4);

const REST_TOKEN: Token = Token(5);
const TRADE_TOKEN: Token = Token(6);

const REST_HOST: &str = "https://api.binance.com";

pub fn run(market_ip: Option<String>, order_ip: Option<String>) -> Result<()> {
    init_latency_stats();

    let mut user_data = false;
    let mut last_test_order_time: u64 = utils::perf::now();
    let mut is_trading_fee_received = false;

    const N: usize = 1024 * 32;
    let callback = move |handle: &mut WebSocketHandle<N>, cd: CallbackData| -> Result<()> {
        let now = utils::perf::now();
        match cd {
            CallbackData::Message(token, msg) => match msg {
                Message::WebsocketPayload(data) => match token {
                    MARKET_DATA_TOKEN => {
                        if let Some(bt) = parse_sbe_bookticker(data.as_ref()) {
                            ArbitrageEngine::update_rate(bt.symbol, bt.bid_price, bt.ask_price);
                            if !is_trading_fee_received {
                                println!("waiting for trading fee ready.");
                                return Ok(());
                            }
                            if !user_data {
                                println!("waiting for user data subscription.");
                                return Ok(());
                            }
                            if circles_to_ns(now - unsafe { CURRENT_RING_TS }) < 5_000_000_000.0 {
                                return Ok(());
                            }
                            if let Some(ring_index) = ArbitrageEngine::check_arbitrage(bt.symbol) {
                                if ArbitrageEngine::compute_orders(ring_index).is_some() {
                                    let buf = handle.get_write_buf(ORDER_TOKEN_1)?;
                                    generate_order_requests(ring_index, now, buf, 0);
                                    handle.trigger_write(ORDER_TOKEN_1)?;
                                    let buf = handle.get_write_buf(ORDER_TOKEN_2)?;
                                    generate_order_requests(ring_index, now, buf, 1);
                                    handle.trigger_write(ORDER_TOKEN_2)?;
                                    let buf = handle.get_write_buf(ORDER_TOKEN_3)?;
                                    generate_order_requests(ring_index, now, buf, 2);
                                    handle.trigger_write(ORDER_TOKEN_3)?;
                                    if unsafe { RING_FILLED_ORDERS[ring_index][0] } == 4 {
                                        let buf = handle.get_write_buf(ORDER_TOKEN_4)?;
                                        generate_order_requests(ring_index, now, buf, 3);
                                        handle.trigger_write(ORDER_TOKEN_4)?;
                                    }
                                } else {
                                    println!("no orders");
                                }
                                unsafe {
                                    CURRENT_RING_INDEX = ring_index;
                                    CURRENT_RING_TS = now;
                                }
                            } else if circles_to_ns(now - last_test_order_time) > 600_000_000.0 {
                                // 定期发送测试订单以保持连接活跃
                                let pair =
                                    TradingPair::from(now as usize % TRADING_PAIR_COUNT as usize);
                                let price = unsafe {
                                    TRADING_PAIR_RATES[pair as usize]
                                        [EdgeDirection::Reverse as usize]
                                };
                                if price <= 0.0 {
                                    return Ok(());
                                }
                                let buf = handle.get_write_buf(ORDER_TOKEN_1)?;
                                let pair_str = pair.to_str();
                                generate_order_request_by_symbol(now, buf, price, pair_str);
                                handle.trigger_write(ORDER_TOKEN_1)?;
                                let buf = handle.get_write_buf(ORDER_TOKEN_2)?;
                                generate_order_request_by_symbol(now, buf, price, pair_str);
                                handle.trigger_write(ORDER_TOKEN_2)?;
                                let buf = handle.get_write_buf(ORDER_TOKEN_3)?;
                                generate_order_request_by_symbol(now, buf, price, pair_str);
                                handle.trigger_write(ORDER_TOKEN_3)?;
                                let buf = handle.get_write_buf(ORDER_TOKEN_4)?;
                                generate_order_request_by_symbol(now, buf, price, pair_str);
                                handle.trigger_write(ORDER_TOKEN_4)?;
                                last_test_order_time = now;
                            }
                        }
                    }
                    TRADE_TOKEN => {
                        if let Some(trade) = parse_sbe_trade(data.as_ref()) {
                            println!("trade: {:?}", trade);
                        }
                    }
                    ORDER_TOKEN_1 | ORDER_TOKEN_2 | ORDER_TOKEN_3 | ORDER_TOKEN_4 => {
                        if !user_data {
                            handle
                                .send_message(token, generate_user_data_sub_request())
                                .unwrap();
                            user_data = true;
                        }
                        engine::monitor::monitor_order_execution(token.0 - 1, data.as_ref());
                    }
                    _ => (),
                },
                Message::HttpResponse(response) => {
                    if let Some(body) = response.body.as_ref() {
                        println!("{:?}", update_trading_fee(body));
                        is_trading_fee_received = true;
                    }
                }
                _ => (),
            },
            CallbackData::ConnectionOpen(token) => match token {
                MARKET_DATA_TOKEN => {
                    println!("subscribe market data done");
                }
                ORDER_TOKEN_1 | ORDER_TOKEN_2 | ORDER_TOKEN_3 | ORDER_TOKEN_4 => {
                    println!("order connection opened: {:?}", token);
                    handle.send_message(token, generate_session_logon_request())?;
                }
                REST_TOKEN => {
                    println!("quering trading fee");
                    handle.send_message(REST_TOKEN, generate_trading_fee_request())?;
                }
                _ => (),
            },
            CallbackData::ConnectionClose(token) => {
                println!("connection close: {:?}", token);
            }
            CallbackData::ConnectionError(token, error) => {
                println!("connection err: {:?}: {:?}", token, error);
            }
        }
        Ok(())
    };
    let mut settings = Settings::default();
    settings.event_loop_timeout = Some(std::time::Duration::from_millis(1));
    let mut websocket = WebSocket::new(settings, callback)?;

    let mut market_data_url: Url = generate_market_data_ws_url().into();
    if let Some(mip) = market_ip {
        market_data_url.socket_addr = Some(SocketAddr::new(
            IpAddr::V4(Ipv4Addr::from_str(&mip).unwrap()),
            market_data_url.port,
        ));
    }
    let mut headers = std::collections::HashMap::new();
    headers.insert("X-MBX-APIKEY".to_string(), API_KEY.to_string());
    websocket.connect_with_headers(market_data_url, MARKET_DATA_TOKEN, headers)?;

    let trade_url: Url = generate_trade_ws_url().into();
    let mut headers = std::collections::HashMap::new();
    headers.insert("X-MBX-APIKEY".to_string(), API_KEY.to_string());
    websocket.connect_with_headers(trade_url, TRADE_TOKEN, headers)?;

    let mut order_url: Url = generate_order_url().into();
    if let Some(oip) = order_ip {
        order_url.socket_addr = Some(SocketAddr::new(
            IpAddr::V4(Ipv4Addr::from_str(&oip).unwrap()),
            order_url.port,
        ));
    }
    websocket.connect(order_url.clone(), ORDER_TOKEN_1)?;
    websocket.connect(order_url.clone(), ORDER_TOKEN_2)?;
    websocket.connect(order_url.clone(), ORDER_TOKEN_3)?;
    websocket.connect(order_url.clone(), ORDER_TOKEN_4)?;
    websocket.connect(REST_HOST, REST_TOKEN)?;

    match websocket.run() {
        Ok(_) => (),
        Err(e) => {
            println!("websocket run error: {:?}", e);
        }
    }
    Ok(())
}
