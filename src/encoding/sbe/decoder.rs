use super::book_ticker::{SbeBookTicker, parse_sbe_bookticker};
use super::schema::{SbeHeader, SbeMessageType};
use super::trade::{SbeTrade, parse_sbe_trade};

/// SBE解码器结果枚举
#[derive(Debug)]
pub enum SbeMessage<'a> {
    BookTicker(SbeBookTicker<'a>),
    Trade(SbeTrade<'a>),
    OrderBookUpdate,
    Heartbeat,
    Unknown,
}

/// SBE解码器
pub struct SbeDecoder;

impl SbeDecoder {
    /// 解码SBE消息
    ///
    /// # Arguments
    /// * `data` - 包含SBE消息的字节数组
    ///
    /// # Returns
    /// * `Some(SbeMessage)` - 成功解码的消息
    /// * `None` - 解码失败或数据不足
    pub fn decode(data: &[u8]) -> Option<SbeMessage> {
        // 检查数据长度是否足够包含头部
        if data.len() < SbeHeader::SIZE {
            return None;
        }

        // 解析SBE头部
        let header = SbeHeader::from_bytes(data)?;

        // 验证头部有效性
        if !header.is_valid() {
            return None;
        }

        // 检查消息体长度（允许实际长度大于block_length，因为可能有额外字段或填充）
        let min_required_length = SbeHeader::SIZE + header.block_length as usize;
        if data.len() < min_required_length {
            return None;
        }

        // 获取完整的消息体数据（包含变长字段）
        let message_data = &data[SbeHeader::SIZE..];

        // 根据消息类型解码
        match header.message_type() {
            SbeMessageType::BookTicker => {
                if let Some(book_ticker) = parse_sbe_bookticker(message_data) {
                    Some(SbeMessage::BookTicker(book_ticker))
                } else {
                    None
                }
            }
            SbeMessageType::BestBidAsk => {
                // BestBidAsk 消息与 BookTicker 格式相同，使用相同的解析器
                if let Some(book_ticker) = parse_sbe_bookticker(message_data) {
                    Some(SbeMessage::BookTicker(book_ticker))
                } else {
                    None
                }
            }
            SbeMessageType::BinanceBestBidAsk => {
                // Binance 实际的 BestBidAsk 消息，与 BookTicker 格式相同
                if let Some(book_ticker) = parse_sbe_bookticker(message_data) {
                    Some(SbeMessage::BookTicker(book_ticker))
                } else {
                    None
                }
            }
            SbeMessageType::Trade => {
                if let Some(trade) = parse_sbe_trade(message_data) {
                    Some(SbeMessage::Trade(trade))
                } else {
                    None
                }
            }
            SbeMessageType::OrderBookUpdate => {
                // TODO: 实现OrderBookUpdate消息解码
                Some(SbeMessage::OrderBookUpdate)
            }
            SbeMessageType::Heartbeat => Some(SbeMessage::Heartbeat),
        }
    }

    /// 批量解码多个SBE消息
    ///
    /// # Arguments
    /// * `data` - 包含多个SBE消息的字节数组
    ///
    /// # Returns
    /// * `Vec<SbeMessage>` - 解码成功的消息列表
    pub fn decode_batch(data: &[u8]) -> Vec<SbeMessage> {
        let mut messages = Vec::new();
        let mut offset = 0;

        while offset < data.len() {
            // 检查剩余数据是否足够包含头部
            if data.len() - offset < SbeHeader::SIZE {
                break;
            }

            // 解析头部
            let header_data = &data[offset..offset + SbeHeader::SIZE];
            if let Some(header) = SbeHeader::from_bytes(header_data) {
                if !header.is_valid() {
                    break;
                }

                let message_length = SbeHeader::SIZE + header.block_length as usize;

                // 检查剩余数据是否足够包含完整消息
                if data.len() - offset < message_length {
                    break;
                }

                // 解码消息
                let message_data = &data[offset..offset + message_length];
                if let Some(message) = Self::decode(message_data) {
                    messages.push(message);
                }

                offset += message_length;
            } else {
                break;
            }
        }

        messages
    }

    /// 验证SBE消息格式
    ///
    /// # Arguments
    /// * `data` - 待验证的字节数组
    ///
    /// # Returns
    /// * `true` - 是有效的SBE消息格式
    /// * `false` - 不是有效的SBE消息格式
    pub fn is_sbe_message(data: &[u8]) -> bool {
        if data.len() < SbeHeader::SIZE {
            return false;
        }

        if let Some(header) = SbeHeader::from_bytes(data) {
            let is_valid = header.is_valid();
            // 允许实际长度大于等于最小要求长度（block_length可能不包含所有字段）
            let min_required_length = SbeHeader::SIZE + header.block_length as usize;
            let has_enough_data = data.len() >= min_required_length;

            is_valid && has_enough_data
        } else {
            false
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_sbe_decoder_validation() {
        // 有效的SBE头部
        let valid_header = [
            0x20, 0x00, // block_length = 32
            0xE9, 0x03, // template_id = 1001
            0x01, 0x00, // schema_id = 1
            0x01, 0x00, // version = 1
        ];

        // 添加32字节的消息体
        let mut valid_message = valid_header.to_vec();
        valid_message.extend_from_slice(&[0u8; 32]);

        assert!(SbeDecoder::is_sbe_message(&valid_message));

        // 无效的头部（schema_id错误）
        let invalid_header = [
            0x20, 0x00, // block_length = 32
            0xE9, 0x03, // template_id = 1001
            0x02, 0x00, // schema_id = 2 (错误)
            0x01, 0x00, // version = 1
        ];

        let mut invalid_message = invalid_header.to_vec();
        invalid_message.extend_from_slice(&[0u8; 32]);

        assert!(!SbeDecoder::is_sbe_message(&invalid_message));

        // 数据长度不足
        assert!(!SbeDecoder::is_sbe_message(&valid_header[..4]));
    }
}
